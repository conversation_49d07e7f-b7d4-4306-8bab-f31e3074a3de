// Fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight, SymbolViewProps } from 'expo-symbols';
import { ComponentProps } from 'react';
import { OpaqueColorValue, type StyleProp, type TextStyle } from 'react-native';

type IconMapping = Record<SymbolViewProps['name'], ComponentProps<typeof MaterialIcons>['name']>;
type IconSymbolName = keyof typeof MAPPING;

/**
 * Add your SF Symbols to Material Icons mappings here.
 * - see Material Icons in the [Icons Directory](https://icons.expo.fyi).
 * - see SF Symbols in the [SF Symbols](https://developer.apple.com/sf-symbols/) app.
 */
const MAPPING = {
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  'line.horizontal.3': 'menu',
  'person.circle.fill': 'account-circle',
  'magnifyingglass': 'search',
  'folder.fill': 'folder',
  'dollarsign.circle.fill': 'attach-money',
  'calendar.fill': 'event',
  'chart.bar.fill': 'bar-chart',
  'chart.line.uptrend.xyaxis': 'trending-up',
  'camera.fill': 'camera-alt',
  'location.fill': 'location-on',
  'person.2.fill': 'people',
  'checkmark.circle.fill': 'check-circle',
  'message.fill': 'message',
  'circle.fill': 'radio-button-checked',
  'circle.dashed': 'radio-button-unchecked',
  'doc.fill': 'description',
  'creditcard.fill': 'credit-card',
} as any as IconMapping;

/**
 * An icon component that uses native SF Symbols on iOS, and Material Icons on Android and web.
 * This ensures a consistent look across platforms, and optimal resource usage.
 * Icon `name`s are based on SF Symbols and require manual mapping to Material Icons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  const iconName = MAPPING[name];
  
  if (!iconName) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }
  
  return <MaterialIcons color={color} size={size} name={iconName} style={style} />;
}
