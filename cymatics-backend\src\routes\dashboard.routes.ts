import { Router } from 'express';
import { dashboardController } from '@/controllers/dashboard.controller';
import { validateQuery } from '@/middleware/validation.middleware';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import Joi from 'joi';

const router = Router();

// All routes require authentication
router.use(authenticateToken);
// Note: Individual routes will have specific role requirements

/**
 * @route   GET /api/dashboard/stats
 * @desc    Get comprehensive dashboard statistics
 * @access  Private (Admin/Manager only - contains financial data)
 */
router.get('/stats', requireRole(['ADMIN', 'MANAGER']), dashboardController.getDashboardStats);

/**
 * @route   GET /api/dashboard/financial-summary
 * @desc    Get financial summary for a specific period
 * @access  Private (Admin/Manager only - financial data)
 */
router.get(
  '/financial-summary',
  requireRole(['ADMIN', 'MANAGER']),
  validateQuery(Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
  })),
  dashboardController.getFinancialSummary
);

/**
 * @route   GET /api/dashboard/today-schedule
 * @desc    Get today's schedule for dashboard
 * @access  Private (All roles can see schedule)
 */
router.get('/today-schedule', dashboardController.getTodaySchedule);

/**
 * @route   GET /api/dashboard/charts/income-expense
 * @desc    Get income vs expense chart data
 * @access  Private (Admin/Manager only - financial data)
 */
router.get(
  '/charts/income-expense',
  requireRole(['ADMIN', 'MANAGER']),
  validateQuery(Joi.object({
    period: Joi.string().valid('6months', '12months').default('6months'),
  })),
  dashboardController.getIncomeExpenseChart
);

/**
 * @route   GET /api/dashboard/charts/project-details
 * @desc    Get project details chart data
 * @access  Private (All roles can see project data)
 */
router.get('/charts/project-details', dashboardController.getProjectDetailsChart);

/**
 * @route   GET /api/dashboard/charts/expense-breakdown
 * @desc    Get expense breakdown chart data
 * @access  Private (Admin/Manager only - financial data)
 */
router.get(
  '/charts/expense-breakdown',
  requireRole(['ADMIN', 'MANAGER']),
  validateQuery(Joi.object({
    period: Joi.string().valid('6months', '12months').default('6months'),
  })),
  dashboardController.getExpenseBreakdownChart
);

/**
 * @route   GET /api/dashboard/charts/monthly-income-expense
 * @desc    Get monthly income vs expense chart data (Django equivalent)
 * @access  Private (Admin/Manager only - financial data)
 */
router.get('/charts/monthly-income-expense', requireRole(['ADMIN', 'MANAGER']), dashboardController.getMonthlyIncomeExpenseChart);

/**
 * @route   GET /api/dashboard/charts/monthly-projects
 * @desc    Get monthly project count chart data (Django equivalent)
 * @access  Private (All roles can see project data)
 */
router.get('/charts/monthly-projects', dashboardController.getMonthlyProjectChart);

/**
 * @route   GET /api/dashboard/charts/expense-pie
 * @desc    Get expense pie chart data (Django equivalent)
 * @access  Private (Admin/Manager only - financial data)
 */
router.get('/charts/expense-pie', requireRole(['ADMIN', 'MANAGER']), dashboardController.getExpensePieChart);

/**
 * @route   GET /api/dashboard/charts/monthly-expenses-stacked
 * @desc    Get monthly expenses stacked bar chart data (Django equivalent)
 * @access  Private (Admin/Manager only - financial data)
 */
router.get('/charts/monthly-expenses-stacked', requireRole(['ADMIN', 'MANAGER']), dashboardController.getMonthlyExpensesStackedChart);

/**
 * @route   GET /api/dashboard/charts/category-expenses
 * @desc    Get category expenses bar chart data (Django equivalent)
 * @access  Private (Admin/Manager only - financial data)
 */
router.get('/charts/category-expenses', requireRole(['ADMIN', 'MANAGER']), dashboardController.getCategoryExpensesChart);

export default router;
