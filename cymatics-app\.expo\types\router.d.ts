/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/assets`; params?: Router.UnknownInputParams; } | { pathname: `/budget-management`; params?: Router.UnknownInputParams; } | { pathname: `/budget`; params?: Router.UnknownInputParams; } | { pathname: `/client-projects`; params?: Router.UnknownInputParams; } | { pathname: `/clients`; params?: Router.UnknownInputParams; } | { pathname: `/create-asset`; params?: Router.UnknownInputParams; } | { pathname: `/create-client`; params?: Router.UnknownInputParams; } | { pathname: `/create-entertainment`; params?: Router.UnknownInputParams; } | { pathname: `/create-expense`; params?: Router.UnknownInputParams; } | { pathname: `/create-income`; params?: Router.UnknownInputParams; } | { pathname: `/create-payment`; params?: Router.UnknownInputParams; } | { pathname: `/create-project`; params?: Router.UnknownInputParams; } | { pathname: `/edit-asset`; params?: Router.UnknownInputParams; } | { pathname: `/edit-client`; params?: Router.UnknownInputParams; } | { pathname: `/edit-entertainment`; params?: Router.UnknownInputParams; } | { pathname: `/edit-expense`; params?: Router.UnknownInputParams; } | { pathname: `/edit-income`; params?: Router.UnknownInputParams; } | { pathname: `/edit-project`; params?: Router.UnknownInputParams; } | { pathname: `/entertainment`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/maps`; params?: Router.UnknownInputParams; } | { pathname: `/pending-payments`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/project-details`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/signup-animated`; params?: Router.UnknownInputParams; } | { pathname: `/status`; params?: Router.UnknownInputParams; } | { pathname: `/user-management`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/calendar` | `/calendar`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/expense` | `/expense`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/income` | `/income`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/projects` | `/projects`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/assets`; params?: Router.UnknownOutputParams; } | { pathname: `/budget-management`; params?: Router.UnknownOutputParams; } | { pathname: `/budget`; params?: Router.UnknownOutputParams; } | { pathname: `/client-projects`; params?: Router.UnknownOutputParams; } | { pathname: `/clients`; params?: Router.UnknownOutputParams; } | { pathname: `/create-asset`; params?: Router.UnknownOutputParams; } | { pathname: `/create-client`; params?: Router.UnknownOutputParams; } | { pathname: `/create-entertainment`; params?: Router.UnknownOutputParams; } | { pathname: `/create-expense`; params?: Router.UnknownOutputParams; } | { pathname: `/create-income`; params?: Router.UnknownOutputParams; } | { pathname: `/create-payment`; params?: Router.UnknownOutputParams; } | { pathname: `/create-project`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-asset`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-client`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-entertainment`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-expense`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-income`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-project`; params?: Router.UnknownOutputParams; } | { pathname: `/entertainment`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/maps`; params?: Router.UnknownOutputParams; } | { pathname: `/pending-payments`; params?: Router.UnknownOutputParams; } | { pathname: `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/project-details`; params?: Router.UnknownOutputParams; } | { pathname: `/register`; params?: Router.UnknownOutputParams; } | { pathname: `/signup-animated`; params?: Router.UnknownOutputParams; } | { pathname: `/status`; params?: Router.UnknownOutputParams; } | { pathname: `/user-management`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/calendar` | `/calendar`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/expense` | `/expense`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/income` | `/income`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/projects` | `/projects`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/assets${`?${string}` | `#${string}` | ''}` | `/budget-management${`?${string}` | `#${string}` | ''}` | `/budget${`?${string}` | `#${string}` | ''}` | `/client-projects${`?${string}` | `#${string}` | ''}` | `/clients${`?${string}` | `#${string}` | ''}` | `/create-asset${`?${string}` | `#${string}` | ''}` | `/create-client${`?${string}` | `#${string}` | ''}` | `/create-entertainment${`?${string}` | `#${string}` | ''}` | `/create-expense${`?${string}` | `#${string}` | ''}` | `/create-income${`?${string}` | `#${string}` | ''}` | `/create-payment${`?${string}` | `#${string}` | ''}` | `/create-project${`?${string}` | `#${string}` | ''}` | `/edit-asset${`?${string}` | `#${string}` | ''}` | `/edit-client${`?${string}` | `#${string}` | ''}` | `/edit-entertainment${`?${string}` | `#${string}` | ''}` | `/edit-expense${`?${string}` | `#${string}` | ''}` | `/edit-income${`?${string}` | `#${string}` | ''}` | `/edit-project${`?${string}` | `#${string}` | ''}` | `/entertainment${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/maps${`?${string}` | `#${string}` | ''}` | `/pending-payments${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/project-details${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `/signup-animated${`?${string}` | `#${string}` | ''}` | `/status${`?${string}` | `#${string}` | ''}` | `/user-management${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/calendar${`?${string}` | `#${string}` | ''}` | `/calendar${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/dashboard${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/expense${`?${string}` | `#${string}` | ''}` | `/expense${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/income${`?${string}` | `#${string}` | ''}` | `/income${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/projects${`?${string}` | `#${string}` | ''}` | `/projects${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/assets`; params?: Router.UnknownInputParams; } | { pathname: `/budget-management`; params?: Router.UnknownInputParams; } | { pathname: `/budget`; params?: Router.UnknownInputParams; } | { pathname: `/client-projects`; params?: Router.UnknownInputParams; } | { pathname: `/clients`; params?: Router.UnknownInputParams; } | { pathname: `/create-asset`; params?: Router.UnknownInputParams; } | { pathname: `/create-client`; params?: Router.UnknownInputParams; } | { pathname: `/create-entertainment`; params?: Router.UnknownInputParams; } | { pathname: `/create-expense`; params?: Router.UnknownInputParams; } | { pathname: `/create-income`; params?: Router.UnknownInputParams; } | { pathname: `/create-payment`; params?: Router.UnknownInputParams; } | { pathname: `/create-project`; params?: Router.UnknownInputParams; } | { pathname: `/edit-asset`; params?: Router.UnknownInputParams; } | { pathname: `/edit-client`; params?: Router.UnknownInputParams; } | { pathname: `/edit-entertainment`; params?: Router.UnknownInputParams; } | { pathname: `/edit-expense`; params?: Router.UnknownInputParams; } | { pathname: `/edit-income`; params?: Router.UnknownInputParams; } | { pathname: `/edit-project`; params?: Router.UnknownInputParams; } | { pathname: `/entertainment`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/maps`; params?: Router.UnknownInputParams; } | { pathname: `/pending-payments`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/project-details`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/signup-animated`; params?: Router.UnknownInputParams; } | { pathname: `/status`; params?: Router.UnknownInputParams; } | { pathname: `/user-management`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/calendar` | `/calendar`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/expense` | `/expense`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/income` | `/income`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/projects` | `/projects`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
