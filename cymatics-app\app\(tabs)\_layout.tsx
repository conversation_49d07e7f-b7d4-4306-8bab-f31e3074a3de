import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useTheme } from '@/contexts/ThemeContext';
import { useUser } from '@/contexts/UserContext';

export default function TabLayout() {
  const { colors } = useTheme();
  const { userData, isAuthenticated } = useUser();

  // Determine which tabs to show based on user role
  const isAdmin = userData?.role === 'ADMIN';
  const isManager = userData?.role === 'MANAGER' || isAdmin;
  const isUser = userData?.role === 'USER' || isManager;

  // Debug logging for role-based tab visibility
  React.useEffect(() => {
    console.log('🔍 TabLayout: User role check', {
      userData: userData ? {
        role: userData.role,
        email: userData.email,
        isAuthenticated
      } : 'null',
      isAdmin,
      isManager,
      isUser,
      showAdminTabs: isAdmin
    });
  }, [userData, isAuthenticated, isAdmin]);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.tabIconSelected,
        tabBarInactiveTintColor: colors.tabIconDefault,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            backgroundColor: colors.background,
            borderTopColor: colors.border,
          },
          default: {
            backgroundColor: colors.background,
            borderTopColor: colors.border,
          },
        }),
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="home"
              size={28}
              color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="projects"
        options={{
          title: 'Projects',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="description"
              size={28}
              color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
            />
          ),
        }}
      />
      {/* Show Income/Expense tabs only for admin users */}
      {(isAuthenticated && isAdmin) && (
        <>
          <Tabs.Screen
            name="income"
            options={{
              title: 'Income',
              tabBarIcon: ({ color, focused }) => (
                <MaterialIcons
                  name="payments"
                  size={28}
                  color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
                />
              ),
            }}
          />
          <Tabs.Screen
            name="expense"
            options={{
              title: 'Expense',
              tabBarIcon: ({ color, focused }) => (
                <MaterialIcons
                  name="attach-money"
                  size={28}
                  color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
                />
              ),
            }}
          />
        </>
      )}
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendar',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="event"
              size={28}
              color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
            />
          ),
        }}
      />
    </Tabs>
  );
}
